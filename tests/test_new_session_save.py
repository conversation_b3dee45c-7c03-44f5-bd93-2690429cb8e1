#!/usr/bin/env python3
"""
测试新会话保存功能
"""
import asyncio
import sqlite3
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from sqlite_data_layer import SQLiteDataLayer
import uuid
from datetime import datetime

async def test_new_session_save():
    """测试新会话保存功能"""
    data_layer = SQLiteDataLayer(db_path="../data/chainlit_history.db")
    
    # 查看保存前的线程数量
    print("=== 保存前的线程列表 ===")
    conn = sqlite3.connect("../data/chainlit_history.db")
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM threads")
    initial_count = cursor.fetchone()[0]
    print(f"初始线程数量: {initial_count}")
    conn.close()
    
    # 创建一个新的线程
    thread_id = str(uuid.uuid4())
    user_id = "test_user_123"
    
    # 首先创建用户（如果不存在）
    try:
        # 创建一个简单的用户对象
        class SimpleUser:
            def __init__(self, identifier, metadata=None):
                self.identifier = identifier
                self.metadata = metadata or {}

        user = SimpleUser(identifier=user_id, metadata={})
        await data_layer.create_user(user)
        print(f"创建用户: {user_id}")
    except Exception as e:
        print(f"用户可能已存在: {e}")

    # 创建线程
    thread_dict = {
        "id": thread_id,
        "name": "测试新会话",
        "userId": user_id,
        "userIdentifier": user_id,
        "createdAt": datetime.now().isoformat() + "Z",
        "metadata": {},
        "tags": []
    }
    
    print(f"\n=== 尝试创建新线程: {thread_id} ===")
    try:
        # 调用创建线程方法
        await data_layer.create_thread(thread_dict)
        print("线程创建方法调用成功")
        
        # 创建一个步骤
        step_dict = {
            "id": str(uuid.uuid4()),
            "name": "test_step",
            "type": "user_message",
            "threadId": thread_id,
            "parentId": None,
            "streaming": False,
            "waitForAnswer": False,
            "isError": False,
            "input": "测试输入",
            "output": "测试输出",
            "createdAt": datetime.now().isoformat() + "Z",
            "metadata": {},
            "tags": [],
            "command": None,
            "start": None,
            "end": None,
            "generation": None,
            "showInput": None,
            "language": None,
            "indent": 0,
            "defaultOpen": False,
            "disableFeedback": False
        }
        
        await data_layer.create_step(step_dict)  # type: ignore
        print("步骤创建方法调用成功")
        
        # 验证保存结果
        print("\n=== 保存后的线程列表 ===")
        conn = sqlite3.connect("../data/chainlit_history.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM threads")
        final_count = cursor.fetchone()[0]
        print(f"最终线程数量: {final_count}")
        
        cursor.execute("SELECT id, name, userId FROM threads WHERE id = ?", (thread_id,))
        new_thread = cursor.fetchone()
        if new_thread:
            print(f"✅ 新线程已保存: ID={new_thread[0]}, 名称={new_thread[1]}, 用户={new_thread[2]}")
        else:
            print("❌ 新线程未找到")
            
        cursor.execute("SELECT COUNT(*) FROM steps WHERE threadId = ?", (thread_id,))
        step_count = cursor.fetchone()[0]
        print(f"该线程的步骤数量: {step_count}")
        
        conn.close()
        
        if final_count > initial_count:
            print("✅ 新会话保存成功！")
        else:
            print("❌ 新会话保存失败")
            
    except Exception as e:
        print(f"❌ 保存过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_new_session_save())
